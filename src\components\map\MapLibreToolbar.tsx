import React, { useState, useEffect, useRef, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import {
  Navigation,
  Layers,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Compass,
  Square,
  Circle,
  Pentagon,
  Pencil,
  Trash2,
  MapPin,
  Crosshair,
  ChevronUp,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  Target,
  Ruler,
  Settings,
  Download,
  Upload,
  Plus,
  Edit3,
  Palette,
  Shield,
  Star,
  Zap,
  Save,
  MoreHorizontal
} from 'lucide-react';
import Button from '@/components/ui/Button';
import LegendEditor, { LegendItem } from './LegendEditor';
import SymbolManager, { MilitarySymbol } from './SymbolManager';

interface MapLibreToolbarProps {
  map: maplibregl.Map;
  activeBaseLayer: string;
  onBaseLayerChange: (layer: string) => void;
  mapState: {
    center: [number, number];
    zoom: number;
    bearing: number;
    pitch: number;
  };
  onToolbarStateChange?: (state: {
    viewshedVisible?: boolean;
    legendVisible?: boolean;
    symbolsVisible?: boolean;
  }) => void;
}

interface ToolbarSection {
  id: string;
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  title: string;
  collapsed: boolean;
  visible: boolean;
}

const MapLibreToolbar: React.FC<MapLibreToolbarProps> = ({
  map,
  activeBaseLayer,
  onBaseLayerChange,
  mapState,
  onToolbarStateChange
}) => {
  // Auto-hide state
  const [isVisible, setIsVisible] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Toolbar sections state - start collapsed for cleaner interface
  const [sections, setSections] = useState<ToolbarSection[]>([
    { id: 'navigation', position: 'top-left', title: 'TOOLS', collapsed: true, visible: true },
    { id: 'layers', position: 'top-right', title: 'LAYERS', collapsed: true, visible: true },
    { id: 'legend', position: 'bottom-left', title: 'LEGEND', collapsed: true, visible: true },
    { id: 'controls', position: 'bottom-right', title: 'CONTROLS', collapsed: true, visible: true }
  ]);

  // Drawing mode state
  const [drawingMode, setDrawingMode] = useState<string | null>(null);

  // Advanced tactical features state
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [viewshedMode, setViewshedMode] = useState(false);
  const [measurementMode, setMeasurementMode] = useState<string | null>(null);
  const [symbolLibraryOpen, setSymbolLibraryOpen] = useState(false);
  const [legendEditMode, setLegendEditMode] = useState(false);
  const [advancedLayersOpen, setAdvancedLayersOpen] = useState(false);

  // Modal component state
  const [legendItems, setLegendItems] = useState<LegendItem[]>([]);
  const [selectedSymbol, setSelectedSymbol] = useState<MilitarySymbol | null>(null);

  // Auto-hide functionality
  const resetAutoHideTimer = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    setIsVisible(true);

    hideTimeoutRef.current = setTimeout(() => {
      if (!isHovered) {
        setIsVisible(false);
      }
    }, 3000); // Hide after 3 seconds of inactivity
  }, [isHovered]);

  // Track user activity
  useEffect(() => {
    const handleActivity = () => {
      resetAutoHideTimer();
    };

    // Add event listeners for user activity
    const events = ['mousemove', 'keydown', 'click', 'wheel'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity);
    });

    // Map-specific events
    if (map) {
      map.on('move', handleActivity);
      map.on('zoom', handleActivity);
      map.on('rotate', handleActivity);
      map.on('pitch', handleActivity);
    }

    // Initial timer
    resetAutoHideTimer();

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });

      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }

      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
    };
  }, [map, resetAutoHideTimer]);

  // Toggle section collapse
  const toggleSection = useCallback((sectionId: string) => {
    setSections(prev => prev.map(section =>
      section.id === sectionId
        ? { ...section, collapsed: !section.collapsed }
        : section
    ));
  }, []);

  // Handle drawing mode change
  const handleDrawingMode = useCallback((mode: string) => {
    setDrawingMode(mode === drawingMode ? null : mode);
    // TODO: Implement actual drawing functionality
  }, [drawingMode]);

  // Handle advanced tool activation
  const handleAdvancedTool = useCallback((toolId: string) => {
    switch (toolId) {
      case 'measure-distance':
      case 'measure-area':
        setMeasurementMode(measurementMode === toolId ? null : toolId);
        break;
      case 'viewshed':
        const newViewshedMode = !viewshedMode;
        setViewshedMode(newViewshedMode);
        onToolbarStateChange?.({ viewshedVisible: newViewshedMode });
        break;
      case 'tactical-symbol':
        setSymbolLibraryOpen(true);
        onToolbarStateChange?.({ symbolsVisible: true });
        break;
      default:
        break;
    }
  }, [measurementMode, viewshedMode, onToolbarStateChange]);

  // Handle modal operations
  const handleOpenModal = useCallback((modalType: string) => {
    setActiveModal(modalType);
  }, []);

  const handleCloseModal = useCallback(() => {
    setActiveModal(null);
    setSymbolLibraryOpen(false);
    setLegendEditMode(false);
    setAdvancedLayersOpen(false);
    // Close all components when modal closes
    onToolbarStateChange?.({
      symbolsVisible: false,
      legendVisible: false
    });
  }, [onToolbarStateChange]);

  // Legend Editor handlers
  const handleLegendSave = useCallback((items: LegendItem[]) => {
    setLegendItems(items);
    // TODO: Integrate with map legend display
    console.log('Legend saved:', items);
  }, []);

  const handleLegendPreview = useCallback((items: LegendItem[]) => {
    // TODO: Show real-time preview on map
    console.log('Legend preview:', items);
  }, []);

  // Symbol Manager handlers
  const handleSymbolSelect = useCallback((symbol: MilitarySymbol) => {
    setSelectedSymbol(symbol);
    console.log('Symbol selected:', symbol);
  }, []);

  const handlePlaceSymbolOnMap = useCallback((symbol: MilitarySymbol, coordinates?: [number, number]) => {
    // TODO: Place symbol on map at specified coordinates or map center
    const coords = coordinates || map.getCenter().toArray() as [number, number];
    console.log('Placing symbol on map:', symbol, 'at coordinates:', coords);

    // For now, just log the action - this would integrate with the map marker system
    // In a full implementation, this would create a new marker with the symbol
  }, [map]);

  // Handle map controls
  const handleZoomIn = useCallback(() => {
    map.zoomIn();
  }, [map]);

  const handleZoomOut = useCallback(() => {
    map.zoomOut();
  }, [map]);

  const handleResetBearing = useCallback(() => {
    map.rotateTo(0);
  }, [map]);

  const handleResetPitch = useCallback(() => {
    map.setPitch(0);
  }, [map]);

  // Base layer options
  const baseLayerOptions = [
    { id: 'satellite', name: 'Satellite', icon: <Layers size={16} /> },
    { id: 'terrain', name: 'Terrain', icon: <Navigation size={16} /> },
    { id: 'streets', name: 'Streets', icon: <MapPin size={16} /> }
  ];

  // Basic drawing tools
  const basicDrawingTools = [
    { id: 'point', name: 'Point', icon: <MapPin size={16} /> },
    { id: 'line', name: 'Line', icon: <Pencil size={16} /> },
    { id: 'polygon', name: 'Polygon', icon: <Pentagon size={16} /> },
    { id: 'rectangle', name: 'Rectangle', icon: <Square size={16} /> },
    { id: 'circle', name: 'Circle', icon: <Circle size={16} /> }
  ];

  // Advanced tactical tools
  const advancedTools = [
    { id: 'measure-distance', name: 'Measure Distance', icon: <Ruler size={16} /> },
    { id: 'measure-area', name: 'Measure Area', icon: <Square size={16} /> },
    { id: 'viewshed', name: 'Viewshed Analysis', icon: <Target size={16} /> },
    { id: 'tactical-symbol', name: 'Tactical Symbol', icon: <Shield size={16} /> }
  ];

  // Render toolbar section
  const renderSection = (section: ToolbarSection) => {
    const baseClasses = `
      absolute bg-gray-900 bg-opacity-95 border border-gray-700 rounded-md shadow-xl
      transition-all duration-300 ease-in-out backdrop-blur-sm
      ${isVisible ? 'opacity-100 translate-x-0 translate-y-0' : getHiddenTransform(section.position)}
      ${section.collapsed ? 'w-auto' : 'min-w-[140px] max-w-[180px]'}
    `;

    const positionClasses = getPositionClasses(section.position);

    return (
      <div
        key={section.id}
        className={`${baseClasses} ${positionClasses}`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Compact Section header */}
        <div className="flex items-center justify-between px-2 py-1 border-b border-gray-700">
          <span className="text-[10px] font-semibold text-gray-400 tracking-wide uppercase">
            {section.title}
          </span>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => toggleSection(section.id)}
            className="p-0.5 h-4 w-4 text-gray-500 hover:text-white"
          >
            {section.collapsed ? (
              getExpandIcon(section.position)
            ) : (
              getCollapseIcon(section.position)
            )}
          </Button>
        </div>

        {/* Compact Section content */}
        {!section.collapsed && (
          <div className="p-1.5">
            {renderSectionContent(section.id)}
          </div>
        )}
      </div>
    );
  };

  // Get position classes for each corner
  const getPositionClasses = (position: string) => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-16 left-4'; // Leave space for coordinates
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return '';
    }
  };

  // Get hidden transform for auto-hide
  const getHiddenTransform = (position: string) => {
    switch (position) {
      case 'top-left':
        return 'opacity-0 -translate-x-4 -translate-y-4';
      case 'top-right':
        return 'opacity-0 translate-x-4 -translate-y-4';
      case 'bottom-left':
        return 'opacity-0 -translate-x-4 translate-y-4';
      case 'bottom-right':
        return 'opacity-0 translate-x-4 translate-y-4';
      default:
        return 'opacity-0';
    }
  };

  // Get expand/collapse icons based on position
  const getExpandIcon = (position: string) => {
    switch (position) {
      case 'top-left':
        return <ChevronRight size={12} />;
      case 'top-right':
        return <ChevronLeft size={12} />;
      case 'bottom-left':
        return <ChevronUp size={12} />;
      case 'bottom-right':
        return <ChevronUp size={12} />;
      default:
        return <ChevronRight size={12} />;
    }
  };

  const getCollapseIcon = (position: string) => {
    switch (position) {
      case 'top-left':
        return <ChevronLeft size={12} />;
      case 'top-right':
        return <ChevronRight size={12} />;
      case 'bottom-left':
        return <ChevronDown size={12} />;
      case 'bottom-right':
        return <ChevronDown size={12} />;
      default:
        return <ChevronLeft size={12} />;
    }
  };

  // Render section content based on section ID
  const renderSectionContent = (sectionId: string) => {
    switch (sectionId) {
      case 'navigation':
        return (
          <div className="space-y-1">
            {/* Basic Drawing Tools */}
            <div className="grid grid-cols-3 gap-1">
              {basicDrawingTools.map(tool => (
                <Button
                  key={tool.id}
                  size="sm"
                  variant={drawingMode === tool.id ? "default" : "ghost"}
                  onClick={() => handleDrawingMode(tool.id)}
                  className="p-1 h-6 w-6"
                  title={tool.name}
                >
                  {React.cloneElement(tool.icon, { size: 12 })}
                </Button>
              ))}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setDrawingMode(null)}
                className="p-1 h-6 w-6 text-red-400 hover:text-red-300"
                title="Clear Drawing"
              >
                <Trash2 size={12} />
              </Button>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-700 my-1"></div>

            {/* Advanced Tactical Tools */}
            <div className="grid grid-cols-2 gap-1">
              {advancedTools.map(tool => (
                <Button
                  key={tool.id}
                  size="sm"
                  variant={
                    (tool.id === 'measure-distance' && measurementMode === 'measure-distance') ||
                    (tool.id === 'measure-area' && measurementMode === 'measure-area') ||
                    (tool.id === 'viewshed' && viewshedMode) ||
                    (tool.id === 'tactical-symbol' && symbolLibraryOpen)
                      ? "default" : "ghost"
                  }
                  onClick={() => handleAdvancedTool(tool.id)}
                  className="p-1 h-6 text-[9px]"
                  title={tool.name}
                >
                  {React.cloneElement(tool.icon, { size: 10 })}
                </Button>
              ))}
            </div>
          </div>
        );

      case 'layers':
        return (
          <div className="space-y-1">
            {/* Base Layers */}
            {baseLayerOptions.map(layer => (
              <Button
                key={layer.id}
                size="sm"
                variant={activeBaseLayer === layer.id ? "default" : "ghost"}
                onClick={() => onBaseLayerChange(layer.id)}
                className="w-full justify-start text-[10px] py-1 h-6"
              >
                {React.cloneElement(layer.icon, { size: 10 })}
                <span className="ml-1">{layer.name}</span>
              </Button>
            ))}

            {/* Divider */}
            <div className="border-t border-gray-700 my-1"></div>

            {/* Advanced Layer Controls */}
            <div className="space-y-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleOpenModal('layer-manager')}
                className="w-full justify-start text-[10px] py-1 h-6"
                title="Advanced Layer Management"
              >
                <Settings size={10} />
                <span className="ml-1">Manage</span>
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleOpenModal('layer-import')}
                className="w-full justify-start text-[10px] py-1 h-6"
                title="Import Layers"
              >
                <Upload size={10} />
                <span className="ml-1">Import</span>
              </Button>
            </div>
          </div>
        );

      case 'legend':
        return (
          <div className="space-y-1">
            {/* Legend Controls */}
            <Button
              size="sm"
              variant={legendEditMode ? "default" : "ghost"}
              onClick={() => {
                const newLegendMode = !legendEditMode;
                setLegendEditMode(newLegendMode);
                onToolbarStateChange?.({ legendVisible: newLegendMode });
              }}
              className="w-full justify-start text-[10px] py-1 h-6"
              title="Toggle Legend Visibility"
            >
              <Eye size={10} />
              <span className="ml-1">Legend</span>
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleOpenModal('legend-editor')}
              className="w-full justify-start text-[10px] py-1 h-6"
              title="Edit Legend"
            >
              <Edit3 size={10} />
              <span className="ml-1">Edit</span>
            </Button>

            {/* Divider */}
            <div className="border-t border-gray-700 my-1"></div>

            {/* Military Symbology */}
            <Button
              size="sm"
              variant={symbolLibraryOpen ? "default" : "ghost"}
              onClick={() => {
                const newSymbolMode = !symbolLibraryOpen;
                setSymbolLibraryOpen(newSymbolMode);
                onToolbarStateChange?.({ symbolsVisible: newSymbolMode });
              }}
              className="w-full justify-start text-[10px] py-1 h-6"
              title="Military Symbols"
            >
              <Shield size={10} />
              <span className="ml-1">NATO</span>
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleOpenModal('symbol-manager')}
              className="w-full justify-start text-[10px] py-1 h-6"
              title="Manage Symbols"
            >
              <Palette size={10} />
              <span className="ml-1">Custom</span>
            </Button>

            {/* Symbol Import/Export */}
            <div className="grid grid-cols-2 gap-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleOpenModal('symbol-import')}
                className="p-1 h-6"
                title="Import Symbols"
              >
                <Upload size={10} />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleOpenModal('symbol-export')}
                className="p-1 h-6"
                title="Export Symbols"
              >
                <Download size={10} />
              </Button>
            </div>
          </div>
        );

      case 'controls':
        return (
          <div className="grid grid-cols-2 gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleZoomIn}
              className="p-1 h-6"
              title="Zoom In"
            >
              <ZoomIn size={12} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleZoomOut}
              className="p-1 h-6"
              title="Zoom Out"
            >
              <ZoomOut size={12} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleResetBearing}
              className="p-1 h-6"
              title="Reset Rotation"
            >
              <Compass size={12} />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleResetPitch}
              className="p-1 h-6"
              title="Reset Tilt"
            >
              <RotateCcw size={12} />
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  // Render modal dialogs for advanced features
  const renderModal = () => {
    const modalBaseClasses = `
      fixed inset-0 z-50 flex items-center justify-center
      bg-black bg-opacity-50 backdrop-blur-sm
    `;

    const modalContentClasses = `
      bg-gray-900 border border-gray-600 rounded-lg shadow-2xl
      max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto
    `;

    switch (activeModal) {
      case 'legend-editor':
        return (
          <LegendEditor
            isOpen={true}
            onClose={handleCloseModal}
            initialItems={legendItems}
            onSave={handleLegendSave}
            onPreview={handleLegendPreview}
          />
        );

      case 'symbol-manager':
        return (
          <SymbolManager
            isOpen={true}
            onClose={handleCloseModal}
            onSymbolSelect={handleSymbolSelect}
            onPlaceOnMap={handlePlaceSymbolOnMap}
          />
        );

      case 'layer-manager':
        return (
          <div className={modalBaseClasses} onClick={handleCloseModal}>
            <div className={modalContentClasses} onClick={(e) => e.stopPropagation()}>
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Layer Manager</h3>
                  <Button size="sm" variant="ghost" onClick={handleCloseModal}>
                    <ChevronRight size={16} />
                  </Button>
                </div>
                <div className="space-y-3 text-sm text-gray-300">
                  <p>Advanced layer management:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Layer visibility controls</li>
                    <li>Opacity and blending modes</li>
                    <li>Layer ordering and grouping</li>
                    <li>Custom layer styling</li>
                  </ul>
                  <div className="pt-3 border-t border-gray-700">
                    <Button size="sm" className="w-full">
                      Open Layer Manager
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Render all toolbar sections */}
      {sections.map(renderSection)}

      {/* Compact coordinate display at bottom center */}
      <div
        className={`
          absolute bottom-2 left-1/2 transform -translate-x-1/2
          bg-gray-900 bg-opacity-95 border border-gray-700 rounded-md px-2 py-0.5
          transition-all duration-300 ease-in-out backdrop-blur-sm
          ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}
        `}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-center space-x-2">
          <div className="text-[9px] font-mono text-gray-400">
            {mapState.center[1].toFixed(4)}, {mapState.center[0].toFixed(4)} | Z{mapState.zoom.toFixed(1)}
            {mapState.bearing !== 0 && ` | B${mapState.bearing.toFixed(0)}°`}
            {mapState.pitch !== 0 && ` | P${mapState.pitch.toFixed(0)}°`}
          </div>

          {/* Active feature indicators */}
          {(viewshedMode || measurementMode || drawingMode || legendEditMode || symbolLibraryOpen) && (
            <div className="flex items-center space-x-1">
              <div className="w-px h-3 bg-gray-600"></div>
              {viewshedMode && (
                <div className="flex items-center text-[8px] text-blue-400">
                  <Target size={8} />
                  <span className="ml-0.5">VS</span>
                </div>
              )}
              {measurementMode && (
                <div className="flex items-center text-[8px] text-green-400">
                  <Ruler size={8} />
                  <span className="ml-0.5">{measurementMode === 'measure-distance' ? 'DIST' : 'AREA'}</span>
                </div>
              )}
              {drawingMode && (
                <div className="flex items-center text-[8px] text-yellow-400">
                  <Pencil size={8} />
                  <span className="ml-0.5">DRAW</span>
                </div>
              )}
              {legendEditMode && (
                <div className="flex items-center text-[8px] text-purple-400">
                  <Eye size={8} />
                  <span className="ml-0.5">LEG</span>
                </div>
              )}
              {symbolLibraryOpen && (
                <div className="flex items-center text-[8px] text-orange-400">
                  <Shield size={8} />
                  <span className="ml-0.5">SYM</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Advanced Feature Modals */}
      {activeModal && renderModal()}
    </>
  );
};

export default MapLibreToolbar;
