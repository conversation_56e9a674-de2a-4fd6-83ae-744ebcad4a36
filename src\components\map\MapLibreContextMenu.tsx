import React, { useEffect, useRef } from 'react';
import maplibregl from 'maplibre-gl';
import {
  MapPin,
  Navigation,
  Crosshair,
  Copy,
  Download,
  Ruler,
  Eye,
  Plus,
  Target,
  Zap
} from 'lucide-react';
import Button from '@/components/ui/Button';

interface MapLibreContextMenuProps {
  x: number;
  y: number;
  lngLat: [number, number];
  map: maplibregl.Map;
  onClose: () => void;
}

const MapLibreContextMenu: React.FC<MapLibreContextMenuProps> = ({
  x,
  y,
  lngLat,
  map,
  onClose
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  
  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);
  
  // Position menu to stay within viewport
  const getMenuPosition = () => {
    const menuWidth = 200;
    const menuHeight = 300;
    const padding = 10;
    
    let adjustedX = x;
    let adjustedY = y;
    
    // Adjust horizontal position
    if (x + menuWidth > window.innerWidth - padding) {
      adjustedX = x - menuWidth;
    }
    
    // Adjust vertical position
    if (y + menuHeight > window.innerHeight - padding) {
      adjustedY = y - menuHeight;
    }
    
    return {
      left: Math.max(padding, adjustedX),
      top: Math.max(padding, adjustedY)
    };
  };
  
  const position = getMenuPosition();
  
  // Menu actions
  const handleCenterHere = () => {
    map.flyTo({ center: lngLat, zoom: map.getZoom() });
    onClose();
  };
  
  const handleZoomIn = () => {
    map.flyTo({ center: lngLat, zoom: map.getZoom() + 1 });
    onClose();
  };
  
  const handleZoomOut = () => {
    map.flyTo({ center: lngLat, zoom: map.getZoom() - 1 });
    onClose();
  };
  
  const handleCopyCoordinates = () => {
    const coordText = `${lngLat[1].toFixed(6)}, ${lngLat[0].toFixed(6)}`;
    navigator.clipboard.writeText(coordText).then(() => {
      console.log('Coordinates copied to clipboard:', coordText);
      // TODO: Show toast notification
    }).catch(err => {
      console.error('Failed to copy coordinates:', err);
    });
    onClose();
  };
  
  const handleAddMarker = () => {
    // Create a temporary marker
    const marker = new maplibregl.Marker({
      color: '#FF4444',
      draggable: true
    })
      .setLngLat(lngLat)
      .addTo(map);
    
    // Add popup with coordinates
    const popup = new maplibregl.Popup({
      closeButton: true,
      closeOnClick: false
    })
      .setLngLat(lngLat)
      .setHTML(`
        <div class="tactical-popup">
          <h3 class="text-sm font-bold text-white mb-2">WAYPOINT</h3>
          <div class="text-xs text-gray-300">
            <div><strong>LAT:</strong> ${lngLat[1].toFixed(6)}</div>
            <div><strong>LNG:</strong> ${lngLat[0].toFixed(6)}</div>
          </div>
          <button onclick="this.closest('.maplibregl-popup').remove(); this.closest('.maplibregl-marker').remove();" 
                  class="mt-2 px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
            Remove
          </button>
        </div>
      `)
      .addTo(map);
    
    onClose();
  };
  
  const handleMeasureDistance = () => {
    // TODO: Implement distance measurement tool
    console.log('Measure distance from:', lngLat);
    onClose();
  };
  
  const handleViewshed = () => {
    // TODO: Implement viewshed analysis
    console.log('Calculate viewshed from:', lngLat);
    onClose();
  };
  
  const handleAddIncident = () => {
    // TODO: Open incident creation form with these coordinates
    console.log('Add incident at:', lngLat);
    onClose();
  };
  
  const handleAddResponse = () => {
    // TODO: Open response creation form with these coordinates
    console.log('Add response at:', lngLat);
    onClose();
  };
  
  const menuItems = [
    {
      label: 'Center Here',
      icon: <Crosshair size={16} />,
      action: handleCenterHere
    },
    {
      label: 'Zoom In',
      icon: <Plus size={16} />,
      action: handleZoomIn
    },
    {
      label: 'Zoom Out',
      icon: <Target size={16} />,
      action: handleZoomOut
    },
    { type: 'separator' },
    {
      label: 'Copy Coordinates',
      icon: <Copy size={16} />,
      action: handleCopyCoordinates
    },
    {
      label: 'Add Marker',
      icon: <MapPin size={16} />,
      action: handleAddMarker
    },
    { type: 'separator' },
    {
      label: 'Measure Distance',
      icon: <Ruler size={16} />,
      action: handleMeasureDistance
    },
    {
      label: 'Viewshed Analysis',
      icon: <Eye size={16} />,
      action: handleViewshed
    },
    { type: 'separator' },
    {
      label: 'Add Incident',
      icon: <Zap size={16} />,
      action: handleAddIncident,
      className: 'text-red-400 hover:text-red-300'
    },
    {
      label: 'Add Response',
      icon: <Navigation size={16} />,
      action: handleAddResponse,
      className: 'text-green-400 hover:text-green-300'
    }
  ];
  
  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-gray-900 bg-opacity-95 border border-gray-600 rounded-lg shadow-lg backdrop-blur-sm min-w-[180px]"
      style={{
        left: position.left,
        top: position.top
      }}
    >
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-600">
        <div className="text-xs font-mono text-gray-300">
          <div><strong>LAT:</strong> {lngLat[1].toFixed(6)}</div>
          <div><strong>LNG:</strong> {lngLat[0].toFixed(6)}</div>
        </div>
      </div>
      
      {/* Menu items */}
      <div className="py-1">
        {menuItems.map((item, index) => {
          if (item.type === 'separator') {
            return (
              <div
                key={index}
                className="h-px bg-gray-600 my-1 mx-2"
              />
            );
          }
          
          return (
            <button
              key={index}
              onClick={item.action}
              className={`
                w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 hover:text-white
                flex items-center space-x-2 transition-colors duration-150
                ${item.className || ''}
              `}
            >
              {item.icon}
              <span>{item.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default MapLibreContextMenu;
